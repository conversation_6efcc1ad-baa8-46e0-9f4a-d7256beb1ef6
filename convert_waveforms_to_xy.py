#!/usr/bin/env python3
"""
<PERSON>ript to convert traditional waveforms from compute_waveform_at_x() to compute_waveform_intensity_at_xy()
This makes them compatible with the new three-stage rendering pipeline.
Removes the old compute_waveform_at_x() function completely after conversion.
"""

import os
import sys
import re
from pathlib import Path
from modules.waveform_manager import WaveformManager, WaveformInfo

def needs_conversion(glsl_code: str) -> bool:
    """Check if a waveform needs conversion from compute_waveform_at_x() to compute_waveform_intensity_at_xy()"""

    # Check if it already has compute_waveform_intensity_at_xy as primary function
    if "compute_waveform_intensity_at_xy" in glsl_code:
        return False

    # Check if it has a non-stub compute_waveform_at_x function
    # A stub function just returns 0.0 or similar
    start_pattern = r'float compute_waveform_at_x\(float x_coord\)\s*\{'
    start_match = re.search(start_pattern, glsl_code)

    if not start_match:
        return False

    # Find the matching closing brace
    start_pos = start_match.end() - 1  # Position of opening brace
    brace_count = 1
    pos = start_pos + 1

    while pos < len(glsl_code) and brace_count > 0:
        if glsl_code[pos] == '{':
            brace_count += 1
        elif glsl_code[pos] == '}':
            brace_count -= 1
        pos += 1

    if brace_count > 0:
        return False

    # Extract the function body (without braces)
    function_body = glsl_code[start_pos + 1:pos - 1].strip()

    # Check if it's just a stub (returns 0.0 or similar simple return)
    stub_patterns = [
        r'^\s*return\s+0\.0\s*;\s*$',
        r'^\s*return\s+0\s*;\s*$',
        r'^\s*return\s+0\.0f\s*;\s*$'
    ]

    for pattern in stub_patterns:
        if re.match(pattern, function_body, re.MULTILINE | re.DOTALL):
            return False

    # If it has substantial code, it needs conversion
    return len(function_body.strip()) > 20


def convert_waveform_code(glsl_code: str, waveform_name: str) -> str:
    """Convert compute_waveform_at_x() to compute_waveform_intensity_at_xy() and remove old function"""

    # Find the compute_waveform_at_x function with proper brace matching
    start_pattern = r'float compute_waveform_at_x\(float x_coord\)\s*\{'
    start_match = re.search(start_pattern, glsl_code)

    if not start_match:
        print(f"Warning: No compute_waveform_at_x function found in {waveform_name}")
        return glsl_code

    # Find the matching closing brace
    start_pos = start_match.end() - 1  # Position of opening brace
    brace_count = 1
    pos = start_pos + 1

    while pos < len(glsl_code) and brace_count > 0:
        if glsl_code[pos] == '{':
            brace_count += 1
        elif glsl_code[pos] == '}':
            brace_count -= 1
        pos += 1

    if brace_count > 0:
        print(f"Warning: Could not find matching brace for {waveform_name}")
        return glsl_code

    # Extract the function body (without braces)
    function_body = glsl_code[start_pos + 1:pos - 1].strip()

    # Create a helper function that wraps the original logic
    helper_function = f"""float compute_waveform_at_x_helper(float x_coord) {{
{_indent_code(function_body, 1)}
}}"""

    # Create new XY function that calls the helper
    new_function = f"""{helper_function}

float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {{
    if (!waveform_enabled || waveform_length <= 0) {{
        return 0.0;
    }}

    // Get the waveform value at this x coordinate using original logic
    float waveform_value = compute_waveform_at_x_helper(x_coord);

    // Render as horizontal waveform line
    float waveform_y = 0.5 + waveform_value * 0.25; // Center at 0.5 with amplitude scaling

    // Create a line with thickness and glow
    float line_thickness = 0.02;
    float glow_radius = 0.08;
    float distance_to_line = abs(y_coord - waveform_y);

    // Core line intensity
    float core_intensity = smoothstep(line_thickness, 0.0, distance_to_line);

    // Glow effect
    float glow_intensity = exp(-distance_to_line * distance_to_line / (glow_radius * glow_radius)) * 0.3;

    return clamp(core_intensity + glow_intensity, 0.0, 1.0);
}}"""

    # Remove the original function completely
    original_function = glsl_code[start_match.start():pos]
    new_glsl_code = glsl_code.replace(original_function, new_function)

    return new_glsl_code


def _indent_code(code: str, levels: int) -> str:
    """Add indentation to code"""
    indent = "    " * levels
    lines = code.split('\n')
    return '\n'.join(indent + line if line.strip() else line for line in lines)




def main():
    print("Converting traditional waveforms to XY format...")

    # Initialize waveform manager
    wm = WaveformManager()

    # Discover all waveforms that need conversion
    print("Scanning for waveforms that need conversion...")
    waveforms_to_convert = []

    for waveform_name in wm.list_waveforms():
        waveform_info = wm.get_waveform(waveform_name)
        if waveform_info and needs_conversion(waveform_info.glsl_code):
            waveforms_to_convert.append(waveform_name)
            print(f"  Found: {waveform_name}")

    print(f"\nFound {len(waveforms_to_convert)} waveforms that need conversion:")
    for name in waveforms_to_convert:
        print(f"  - {name}")

    if not waveforms_to_convert:
        print("No waveforms need conversion. All are already using compute_waveform_intensity_at_xy()!")
        return

    print(f"\nStarting conversion of {len(waveforms_to_convert)} waveforms...")

    converted_count = 0
    failed_count = 0

    for waveform_name in waveforms_to_convert:
        try:
            print(f"Converting {waveform_name}...")

            # Load the existing waveform
            waveform_info = wm.get_waveform(waveform_name)
            if not waveform_info:
                print(f"  Warning: Waveform {waveform_name} not found, skipping")
                continue

            # Convert the GLSL code
            original_code = waveform_info.glsl_code
            converted_code = convert_waveform_code(original_code, waveform_name)

            if converted_code == original_code:
                print(f"  Warning: No changes made to {waveform_name}")
                continue

            # Create new waveform info with converted code
            new_waveform_info = WaveformInfo(
                name=waveform_info.name,
                category=waveform_info.category,
                description=waveform_info.description,  # Don't modify description
                complexity=waveform_info.complexity,
                author=waveform_info.author,
                version=waveform_info.version,
                glsl_code=converted_code,
                is_builtin=waveform_info.is_builtin
            )

            # Save the converted waveform
            subdirectory = wm.get_waveform_subdirectory(waveform_name)
            success = wm.save_waveform(new_waveform_info, overwrite=True, subdirectory=subdirectory)

            if success:
                print(f"  ✓ Successfully converted {waveform_name}")
                converted_count += 1
            else:
                print(f"  ✗ Failed to save converted {waveform_name}")
                failed_count += 1

        except Exception as e:
            print(f"  ✗ Error converting {waveform_name}: {e}")
            failed_count += 1

    print(f"\nConversion complete!")
    print(f"Successfully converted: {converted_count}")
    print(f"Failed: {failed_count}")
    print(f"Total processed: {len(waveforms_to_convert)}")

    # Refresh waveform manager to pick up changes
    wm.discover_waveforms()
    print(f"Waveform manager refreshed. Total waveforms: {len(wm.list_waveforms())}")

if __name__ == "__main__":
    main()
